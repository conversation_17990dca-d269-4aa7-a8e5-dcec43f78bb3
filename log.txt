------------------------------------------------------------------------------
You may only use the C/C++ Extension for Visual Studio Code with Visual Studio
Code, Visual Studio or Visual Studio for Mac software to help you develop and
test your applications.
------------------------------------------------------------------------------
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\hl-whiteboard-qt.exe'. Symbols loaded.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\ntdll.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\kernel32.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\KernelBase.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\user32.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\win32u.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\gdi32.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\gdi32full.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\Qt6SvgWidgetsd.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\msvcp_win.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\Qt6Xmld.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\QCefView.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\ucrtbase.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\shell32.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\WinTypes.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\combase.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\shlwapi.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\rpcrt4.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\libzmq-v143-mt-gd-4_3_5.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\msvcrt.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\ole32.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\ws2_32.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\advapi32.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\Qt6Networkd.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\sechost.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\Qt6Svgd.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\Qt6OpenGLWidgetsd.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\Qt6Widgetsd.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\Qt6Guid.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\Qt6Cored.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\oleaut32.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\vcruntime140d.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\msvcp140d.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\ucrtbased.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\D3DCompiler_47.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\d3d11.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\vcruntime140_1d.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\dbghelp.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\WinSxS\amd64_microsoft.windows.gdiplus_6595b64144ccf1df_1.1.26100.3323_none_6ef3737c3dc6be5e\GdiPlus.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\version.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\IPHLPAPI.DLL'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\secur32.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\IPHLPAPI.DLL'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\dnsapi.dll'.
Unloaded 'C:\Windows\System32\IPHLPAPI.DLL'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\winhttp.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\msvcp140_1d.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\Qt6OpenGLd.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\uxtheme.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\dwmapi.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\dxgi.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\D3D12.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\DWrite.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\msvcp140_2d.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\authz.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\userenv.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\mpr.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\netapi32.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\winmm.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\cryptsp.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\dbgcore.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\cryptbase.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\srvcli.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\netutils.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\sspicli.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\imm32.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\directxdatabasehelper.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\kernel.appcore.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\DXCore.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\cfgmgr32.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\bcryptprimitives.dll'.
Unloaded 'C:\Windows\System32\cfgmgr32.dll'.
Unloaded 'C:\Windows\System32\DXCore.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\nsi.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\windows.storage.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\SHCore.dll'.
logPath: C:/Users/<USER>/AppData/Roaming/HL-Whiteboard-Qt/logs/hl-whiteboard-qt.log
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\profapi.dll'.
2025-08-02 09:52:48.444  debug [Log::init:50] 41300:32616 - 日志初始化完成，日志位置： "C:/Users/<USER>/AppData/Roaming/HL-Whiteboard-Qt/logs/hl-whiteboard-qt.log"
2025-08-02 09:52:48.444  debug [CrashReportWin::init:17] 41300:32616 - init win crash report
2025-08-02 09:52:48.445  debug [main:71] 41300:32616 - [MAIN] 全局OpenGL格式配置完成，MSAA级别: 4
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\platforms\qwindowsd.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\setupapi.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\comdlg32.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\wtsapi32.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\d3d9.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\UIAutomationCore.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_5.82.26100.1882_none_87f34cef7a28f535\comctl32.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\DXCore.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\powrprof.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\umpdc.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\msctf.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\devobj.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\cfgmgr32.dll'. 
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\cfgmgr32.dll'. 
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\cfgmgr32.dll'.
Unloaded 'C:\Windows\System32\cfgmgr32.dll'.
Unloaded 'C:\Windows\System32\cfgmgr32.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\wintrust.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\crypt32.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\msasn1.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\clbcatq.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\Windows.UI.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\Windows.UI.Immersive.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\styles\qmodernwindowsstyled.dll'.
2025-08-02 09:52:48.517 hl-whiteboard-qt debug [main:87] 41300:32616 - [OPENGL] OpenGL加速配置完成
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\opengl32.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\glu32.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\Windows.StateRepositoryCore.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_c37f6a93ec21d54c\igxelpicd64.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_c37f6a93ec21d54c\igdml64.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\ResourcePolicyClient.dll'.
Unloaded 'C:\Windows\System32\ResourcePolicyClient.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_c37f6a93ec21d54c\igdgmm64.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\ControlLib.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_c37f6a93ec21d54c\IntelControlLib.dll'.
Unloaded 'C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_c37f6a93ec21d54c\IntelControlLib.dll'.
Unloaded 'C:\Windows\System32\ControlLib.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_c37f6a93ec21d54c\igc64.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\d3d9on12.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\D3D12Core.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_c37f6a93ec21d54c\igd12umd64.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_c37f6a93ec21d54c\igd12um64xel.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\bcrypt.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\ControlLib.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_c37f6a93ec21d54c\IntelControlLib.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\D3DSCache.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_c37f6a93ec21d54c\igd12dxva64.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_c37f6a93ec21d54c\igdinfo64.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\twinapi.appcore.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\dxilconv.dll'.
Unloaded 'C:\Windows\System32\dxilconv.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\ResourcePolicyClient.dll'.
Unloaded 'C:\Windows\System32\ResourcePolicyClient.dll'.
Unloaded 'C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_c37f6a93ec21d54c\igdinfo64.dll'.
Unloaded 'C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_c37f6a93ec21d54c\IntelControlLib.dll'.
Unloaded 'C:\Windows\System32\ControlLib.dll'.
Unloaded 'C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_c37f6a93ec21d54c\igd12um64xel.dll'.
Unloaded 'C:\Windows\System32\DriverStore\FileRepository\iigd_dch.inf_amd64_c37f6a93ec21d54c\igd12umd64.dll'.
Unloaded 'C:\Windows\System32\d3d9on12.dll'.
2025-08-02 09:52:48.689 hl-whiteboard-qt debug [main:88] 41300:32616 - [OPENGL] OpenGL版本: 0
2025-08-02 09:52:48.690 HL-Whiteboard-Qt debug [SingleInstanceManager::initializeSharedMemory:49] 41300:32616 - SingleInstanceManager: 这是第一个实例
2025-08-02 09:52:48.693 HL-Whiteboard-Qt debug [SingleInstanceManager::startServer:97] 41300:32616 - SingleInstanceManager: 本地服务器启动成功，监听: "HL-Whiteboard-Qt_SingleInstance_66b75671"
2025-08-02 09:52:48.694 HL-Whiteboard-Qt debug [main:148] 41300:32616 - 【ipc】 ipcPath: "ipc://C:/Users/<USER>/.hl-ipc-hl-white-board"
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\mswsock.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\wshunix.dll'.
2025-08-02 09:52:48.704 HL-Whiteboard-Qt debug [ScreenAdaptationConstants::ScreenAdaptationManager::calculateScaleFactor:100] 41300:32616 - 缩放计算详情:
2025-08-02 09:52:48.704 HL-Whiteboard-Qt debug [ScreenAdaptationConstants::ScreenAdaptationManager::calculateScaleFactor:101] 41300:32616 -   宽度比例: 0.375
2025-08-02 09:52:48.704 HL-Whiteboard-Qt debug [ScreenAdaptationConstants::ScreenAdaptationManager::calculateScaleFactor:102] 41300:32616 -   高度比例: 0.4
2025-08-02 09:52:48.704 HL-Whiteboard-Qt debug [ScreenAdaptationConstants::ScreenAdaptationManager::calculateScaleFactor:103] 41300:32616 -   基础缩放: 0.375
2025-08-02 09:52:48.704 HL-Whiteboard-Qt debug [ScreenAdaptationConstants::ScreenAdaptationManager::calculateScaleFactor:104] 41300:32616 -   DPI缩放: 1
2025-08-02 09:52:48.704 HL-Whiteboard-Qt debug [ScreenAdaptationConstants::ScreenAdaptationManager::calculateScaleFactor:105] 41300:32616 -   最终缩放因子: 0.375
2025-08-02 09:52:48.704 HL-Whiteboard-Qt debug [ScreenAdaptationConstants::ScreenAdaptationManager::initialize:12] 41300:32616 - === 屏幕适配管理器初始化 ===
2025-08-02 09:52:48.704 HL-Whiteboard-Qt debug [ScreenAdaptationConstants::ScreenAdaptationManager::initialize:13] 41300:32616 - 屏幕尺寸: QSize(1536, 864)
2025-08-02 09:52:48.704 HL-Whiteboard-Qt debug [ScreenAdaptationConstants::ScreenAdaptationManager::initialize:14] 41300:32616 - 屏幕类型: "自定义 (1536x864)"
2025-08-02 09:52:48.706 HL-Whiteboard-Qt debug [AsyncLogWriter::writeEntry:111] 41300:40160 - [2025-08-02 09:52:48.703] [信息] 日志系统初始化完成
2025-08-02 09:52:48.706 HL-Whiteboard-Qt debug [ScreenAdaptationConstants::ScreenAdaptationManager::initialize:15] 41300:32616 - 设备像素比例: 1.25
2025-08-02 09:52:48.706 HL-Whiteboard-Qt debug [ScreenAdaptationConstants::ScreenAdaptationManager::initialize:16] 41300:32616 - 逻辑DPI: 96
2025-08-02 09:52:48.706 HL-Whiteboard-Qt debug [ScreenAdaptationConstants::ScreenAdaptationManager::initialize:17] 41300:32616 - 物理DPI: 126.353
2025-08-02 09:52:48.706 HL-Whiteboard-Qt debug [ScreenAdaptationConstants::ScreenAdaptationManager::initialize:18] 41300:32616 - 计算的缩放因子: 0.375
2025-08-02 09:52:48.706 HL-Whiteboard-Qt debug [ScreenAdaptationConstants::ScreenAdaptationManager::initialize:19] 41300:32616 - 是否高DPI: false
2025-08-02 09:52:48.706 HL-Whiteboard-Qt debug [ScreenAdaptationConstants::ScreenAdaptationManager::initialize:20] 41300:32616 - ========================
2025-08-02 09:52:48.706 HL-Whiteboard-Qt debug [IconFontManager::IconFontManager:22] 41300:32616 - IconFontManager initialized
2025-08-02 09:52:48.707 HL-Whiteboard-Qt debug [IconFontManager::loadFont:68] 41300:32616 - Font loaded successfully: "iconfont" from ":/fonts/iconfont/iconfont.ttf"
2025-08-02 09:52:48.708 HL-Whiteboard-Qt debug [IconFontManager::loadDefaultIconFont:79] 41300:32616 - Default iconfont loaded successfully from resources
2025-08-02 09:52:48.708 HL-Whiteboard-Qt debug [main:172] 41300:32616 - IconFontManager初始化成功
2025-08-02 09:52:48.708 HL-Whiteboard-Qt debug [main:201] 41300:32616 - [MAIN] 全局QCefContext配置完成
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\CefView\libcef.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\winspool.drv'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\CefView\chrome_elf.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\dhcpcsvc.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\dpapi.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\nlansp_c.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\dhcpcsvc6.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\TextInputFramework.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\wkscli.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\twinapi.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\wevtapi.dll'.
[41300:21480:0802/095249.699:ERROR:system_session_analyzer_win.cc(180)] Failed to retrieve events.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\winsta.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\mdmregistration.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\msvcp110_win.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\MMDevAPI.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\omadmapi.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\ntmarta.dll'.
Unloaded 'C:\Windows\System32\msvcp110_win.dll'.
Unloaded 'C:\Windows\System32\omadmapi.dll'.
Unloaded 'C:\Windows\System32\mdmregistration.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\dsreg.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\mscms.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\FirewallAPI.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\fwbase.dll'.
Exception thrown at 0x00007FF9DD57AB6A (KernelBase.dll) in hl-whiteboard-qt.exe: 0x000006BA: RPC 服务器不可用。.

DevTools listening on ws://127.0.0.1:9000/devtools/browser/7d822800-fc14-47ba-9da9-415381e8191f
2025-08-02 09:52:49.813 HL-Whiteboard-Qt debug [main:206] 41300:32616 - [MAIN] 全局QCefContext实例创建完成
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\rsaenh.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\gpapi.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\DataExchange.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\CoreMessaging.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\CoreUIComponents.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\oleacc.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\sxs.dll'.
mincore\com\oleaut32\dispatch\ups.cpp(2126)\OLEAUT32.dll!00007FF9DFB0ECCD: (caller: 00007FF9DFB0F352) ReturnHr(1) tid(7f68) 8002801D 库没有注册。
2025-08-02 09:52:49.915 HL-Whiteboard-Qt debug [WhiteboardView::setupWindowTransparency:1238] 41300:32616 - WhiteboardView: 透明属性设置完成
2025-08-02 09:52:49.920 HL-Whiteboard-Qt warning [WhiteBoardWidget::setupOpenGLViewport:1654] 41300:32616 - [OpenGL] OpenGL不可用，使用软件渲染
2025-08-02 09:52:49.927 HL-Whiteboard-Qt warning [CommandManager::initialize:64] 41300:32616 - [COMMAND_MANAGER] Already initialized
2025-08-02 09:52:49.927 HL-Whiteboard-Qt debug [ScreenUtils::getScreenSize:12] 41300:32616 - 【ScreenUtils】首次调用，初始化屏幕信息
2025-08-02 09:52:49.927 HL-Whiteboard-Qt debug [ScreenUtils::initializeScreenInfo:51] 41300:32616 - 【ScreenUtils】屏幕信息初始化完成:
2025-08-02 09:52:49.927 HL-Whiteboard-Qt debug [ScreenUtils::initializeScreenInfo:52] 41300:32616 -   屏幕尺寸: QSize(1536, 864)
2025-08-02 09:52:49.927 HL-Whiteboard-Qt debug [ScreenUtils::initializeScreenInfo:53] 41300:32616 -   屏幕几何: QRect(0,0 1536x864)
2025-08-02 09:52:49.927 HL-Whiteboard-Qt debug [ScreenUtils::getScreenSize:15] 41300:32616 - 【ScreenUtils】返回屏幕尺寸: QSize(1536, 864)
2025-08-02 09:52:49.928 HL-Whiteboard-Qt debug [WhiteBoardWidget::setMultiTouchEnabled:1349] 41300:32616 - [MULTITOUCH] Multi-touch enabled
2025-08-02 09:52:49.957 HL-Whiteboard-Qt debug [WhiteboardView::initializeBottomBar:1420] 41300:32616 - WhiteboardView: 立即显示底部操作栏
2025-08-02 09:52:49.957 HL-Whiteboard-Qt debug [WhiteboardView::initializeBottomBar:1423] 41300:32616 - WhiteboardView: 底部操作栏初始化完成（独立窗口模式）
2025-08-02 09:52:49.957 HL-Whiteboard-Qt debug [SvgManager::SvgManager:16] 41300:32616 - SvgManager: 创建SVG管理器
2025-08-02 09:52:49.958 HL-Whiteboard-Qt debug [SvgManager::loadSvg:93] 41300:32616 - SvgManager: 成功加载SVG: ":/images/floatmenu/floatmenu-arc-pen-line-icon.svg" ID: "pen_icon_0"
2025-08-02 09:52:49.961 HL-Whiteboard-Qt debug [SvgManager::loadSvg:93] 41300:32616 - SvgManager: 成功加载SVG: ":/images/floatmenu/floatmenu-arc-pen-dashedline-icon.svg" ID: "pen_icon_1"
2025-08-02 09:52:49.961 HL-Whiteboard-Qt debug [SvgManager::loadSvg:93] 41300:32616 - SvgManager: 成功加载SVG: ":/images/floatmenu/floatmenu-arc-pen-marker-icon.svg" ID: "pen_icon_2"
2025-08-02 09:52:49.962 HL-Whiteboard-Qt debug [SvgManager::updateRenderer:294] 41300:32616 - SvgManager: 成功更新渲染器，ID: "pen_icon_0"
2025-08-02 09:52:49.963 HL-Whiteboard-Qt debug [SvgManager::updateRenderer:294] 41300:32616 - SvgManager: 成功更新渲染器，ID: "pen_icon_0"
2025-08-02 09:52:49.964 HL-Whiteboard-Qt debug [IconFontManager::loadFont:68] 41300:32616 - Font loaded successfully: "iconfont" from ":/fonts/iconfont/iconfont.ttf"
2025-08-02 09:52:49.964 HL-Whiteboard-Qt debug [IconFontManager::loadDefaultIconFont:79] 41300:32616 - Default iconfont loaded successfully from resources
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\imageformats\qgifd.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\imageformats\qicnsd.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\imageformats\qicod.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\imageformats\qjpegd.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\imageformats\qsvgd.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\imageformats\qtgad.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\imageformats\qtiffd.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\imageformats\qwbmpd.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Users\<USER>\Desktop\project\gitlab\hl-whiteboard-qt-cursor\hl-whiteboard-qt\build\Debug\imageformats\qwebpd.dll'.
2025-08-02 09:52:50.003 HL-Whiteboard-Qt debug [ArcContentManager::updateArcContentPosition:232] 41300:32616 - ArcContentManager: 更新arc内容位置 "pen" 尺寸: 403 位置: QPointF(-201.5,-201.5) 圆盘中心: QPointF(0,0) 偏移: QPointF(0,0)
2025-08-02 09:52:50.037 HL-Whiteboard-Qt debug [ArcContentManager::updateArcContentPosition:232] 41300:32616 - ArcContentManager: 更新arc内容位置 "eraser" 尺寸: 303 位置: QPointF(-151.5,-151.5) 圆盘中心: QPointF(0,0) 偏移: QPointF(0,0)
2025-08-02 09:52:50.038 HL-Whiteboard-Qt debug [IconFontManager::loadFont:68] 41300:32616 - Font loaded successfully: "iconfont" from ":/fonts/iconfont/iconfont.ttf"
2025-08-02 09:52:50.038 HL-Whiteboard-Qt debug [IconFontManager::loadDefaultIconFont:79] 41300:32616 - Default iconfont loaded successfully from resources
2025-08-02 09:52:50.039 HL-Whiteboard-Qt debug [FloatMenuWidget::setInitialPosition:430] 41300:32616 - FloatMenuWidget: 设置初始位置为屏幕左下角: QPoint(20,440) 屏幕几何: QRect(0,0 1536x816) 窗口大小: QSize(585, 355)
2025-08-02 09:52:50.360 HL-Whiteboard-Qt debug [ArcContentManager::updateArcContentPosition:232] 41300:32616 - ArcContentManager: 更新arc内容位置 "eraser" 尺寸: 303 位置: QPointF(26,66) 圆盘中心: QPointF(177.5,217.5) 偏移: QPointF(0,0)
2025-08-02 09:52:50.360 HL-Whiteboard-Qt debug [ArcContentManager::updateArcContentPosition:232] 41300:32616 - ArcContentManager: 更新arc内容位置 "pen" 尺寸: 403 位置: QPointF(-24,16) 圆盘中心: QPointF(177.5,217.5) 偏移: QPointF(0,0)
2025-08-02 09:52:50.360 HL-Whiteboard-Qt debug [FloatMenuWidget::showEvent:1288] 41300:32616 - FloatMenuWidget: showEvent - 窗口显示
2025-08-02 09:52:50.361 HL-Whiteboard-Qt debug [FloatMenuWidget::showFloatingWindow:592] 41300:32616 - FloatMenuWidget: 显示独立窗口，位置: QPoint(20,440) 大小: QSize(585, 355)
2025-08-02 09:52:50.361 HL-Whiteboard-Qt debug [WhiteboardView::initializeFloatMenu:1253] 41300:32616 - WhiteboardView: 立即显示浮动菜单
2025-08-02 09:52:50.361 HL-Whiteboard-Qt debug [WhiteboardView::initializeFloatMenu:1256] 41300:32616 - WhiteboardView 浮动菜单初始化完成（子窗口模式）
2025-08-02 09:52:50.363 HL-Whiteboard-Qt debug [ScreenUtils::getScreenSize:15] 41300:32616 - 【ScreenUtils】返回屏幕尺寸: QSize(1536, 864)
2025-08-02 09:52:50.363 HL-Whiteboard-Qt debug [ScreenUtils::getScreenSize:15] 41300:32616 - 【ScreenUtils】返回屏幕尺寸: QSize(1536, 864)
2025-08-02 09:52:50.365 HL-Whiteboard-Qt debug [ScreenUtils::getScreenSize:15] 41300:32616 - 【ScreenUtils】返回屏幕尺寸: QSize(1536, 864)
2025-08-02 09:52:50.365 HL-Whiteboard-Qt debug [ScreenUtils::getScreenSize:15] 41300:32616 - 【ScreenUtils】返回屏幕尺寸: QSize(1536, 864)
2025-08-02 09:52:50.366 HL-Whiteboard-Qt debug [ScreenUtils::getScreenSize:15] 41300:32616 - 【ScreenUtils】返回屏幕尺寸: QSize(1536, 864)
2025-08-02 09:52:50.369 HL-Whiteboard-Qt debug [ScreenUtils::getScreenSize:15] 41300:32616 - 【ScreenUtils】返回屏幕尺寸: QSize(1536, 864)
2025-08-02 09:52:50.375 HL-Whiteboard-Qt debug [ScreenUtils::getScreenSize:15] 41300:32616 - 【ScreenUtils】返回屏幕尺寸: QSize(1536, 864)
2025-08-02 09:52:50.380 HL-Whiteboard-Qt debug [ScreenUtils::getScreenSize:15] 41300:32616 - 【ScreenUtils】返回屏幕尺寸: QSize(1536, 864)
2025-08-02 09:52:50.384 HL-Whiteboard-Qt debug [WhiteboardView::initializeSideBar:1291] 41300:32616 - WhiteboardView: 注册初始化监听器
2025-08-02 09:52:50.385 HL-Whiteboard-Qt debug [WhiteboardView::initializeSideBar:1305] 41300:32616 - WhiteboardView: 侧边栏初始化完成（独立窗口模式）
2025-08-02 09:52:50.385 HL-Whiteboard-Qt debug [WhiteboardView::connectSignalsAndSlots:1591] 41300:32616 - WhiteboardView 信号连接完成
2025-08-02 09:52:50.385 HL-Whiteboard-Qt debug [WhiteboardView::registerAllComponentsToZIndexManager:1657] 41300:32616 - WhiteboardView: 开始注册所有组件到ZIndexManager
2025-08-02 09:52:50.385 HL-Whiteboard-Qt debug [ZIndexManager::registerComponent:67] 41300:32616 - "ZIndexManager: 注册组件 [WhiteBoardView] 到层级 0 (类型: 0)"
2025-08-02 09:52:50.390 HL-Whiteboard-Qt debug [ZIndexManager::registerComponent:67] 41300:32616 - "ZIndexManager: 注册组件 [BottomBarWidget] 到层级 100 (类型: 3)"
2025-08-02 09:52:50.391 HL-Whiteboard-Qt debug [ZIndexManager::installActivationBlocker:586] 41300:32616 - "ZIndexManager: 为窗口 [BottomBarWidget] 安装激活拦截器，允许激活: 0"
2025-08-02 09:52:50.393 HL-Whiteboard-Qt debug [FloatMenuWidget::showEvent:1288] 41300:32616 - FloatMenuWidget: showEvent - 窗口显示
2025-08-02 09:52:50.396 HL-Whiteboard-Qt debug [ZIndexManager::registerComponent:67] 41300:32616 - "ZIndexManager: 注册组件 [FloatMenuWidget] 到层级 2001 (类型: 1)"
2025-08-02 09:52:50.396 HL-Whiteboard-Qt debug [ZIndexManager::installActivationBlocker:586] 41300:32616 - "ZIndexManager: 为窗口 [FloatMenuWidget] 安装激活拦截器，允许激活: 0"
2025-08-02 09:52:50.400 HL-Whiteboard-Qt debug [ZIndexManager::registerComponent:67] 41300:32616 - "ZIndexManager: 注册组件 [SideBarWidget] 到层级 3450 (类型: 6)"
2025-08-02 09:52:50.400 HL-Whiteboard-Qt debug [ZIndexManager::installActivationBlocker:586] 41300:32616 - "ZIndexManager: 为窗口 [SideBarWidget] 安装激活拦截器，允许激活: 0"
2025-08-02 09:52:50.401 HL-Whiteboard-Qt debug [WhiteboardView::registerAllComponentsToZIndexManager:1707] 41300:32616 - WhiteboardView: 组件注册完成
2025-08-02 09:52:50.401 HL-Whiteboard-Qt debug [WhiteboardView::setupComponentZOrder:1636] 41300:32616 - WhiteboardView: 通过ZIndexManager应用组件Z-Order
2025-08-02 09:52:50.401 HL-Whiteboard-Qt debug [WhiteboardView::initializeUILayerManagement:1601] 41300:32616 - WhiteboardView: UI层级管理初始化完成（简化版，无定时器）
2025-08-02 09:52:50.401 HL-Whiteboard-Qt info [ZmqServer::registerHandler:287] 41300:32616 - ZmqServer registerHandler:  "beginclass"
2025-08-02 09:52:50.401 HL-Whiteboard-Qt info [ZmqServer::registerHandler:287] 41300:32616 - ZmqServer registerHandler:  "createCef"
2025-08-02 09:52:50.401 HL-Whiteboard-Qt info [ZmqServer::registerHandler:287] 41300:32616 - ZmqServer registerHandler:  "qt.updateResourceStatus"
2025-08-02 09:52:50.401 HL-Whiteboard-Qt info [ZmqServer::registerHandler:287] 41300:32616 - ZmqServer registerHandler:  "qt.insertImageWhiteboard"
2025-08-02 09:52:50.401 HL-Whiteboard-Qt info [ZmqServer::registerHandler:287] 41300:32616 - ZmqServer registerHandler:  "qt.openResourceDialog"
2025-08-02 09:52:50.401 HL-Whiteboard-Qt info [ZmqServer::registerHandler:287] 41300:32616 - ZmqServer registerHandler:  "qt.activeResourceSelection"
2025-08-02 09:52:50.401 HL-Whiteboard-Qt info [ZmqServer::registerHandler:287] 41300:32616 - ZmqServer registerHandler:  "qt.activeDrawSelection"
2025-08-02 09:52:50.401 HL-Whiteboard-Qt info [ZmqServer::registerHandler:287] 41300:32616 - ZmqServer registerHandler:  "qt.openClassSummaryDialog"
2025-08-02 09:52:50.401 HL-Whiteboard-Qt info [ZmqServer::registerHandler:287] 41300:32616 - ZmqServer registerHandler:  "qt.showAISummaryOctopus"
2025-08-02 09:52:50.401 HL-Whiteboard-Qt info [ZmqServer::registerHandler:287] 41300:32616 - ZmqServer registerHandler:  "qt.closeAISummaryOctopus"
2025-08-02 09:52:50.401 HL-Whiteboard-Qt info [ZmqServer::registerHandler:287] 41300:32616 - ZmqServer registerHandler:  "qt.startRotateAISummaryOctopus"
2025-08-02 09:52:50.401 HL-Whiteboard-Qt info [ZmqServer::registerHandler:287] 41300:32616 - ZmqServer registerHandler:  "qt.stopRotateAISummaryOctopus"
2025-08-02 09:52:50.401 HL-Whiteboard-Qt info [ZmqServer::registerHandler:287] 41300:32616 - ZmqServer registerHandler:  "qt.showAISummaryMsg"
2025-08-02 09:52:50.404 HL-Whiteboard-Qt debug [JSBridge::registerAsyncHandler:203] 41300:32616 - JSBridge Registered async handler: "transComplete"
2025-08-02 09:52:50.404 HL-Whiteboard-Qt debug [JSBridge::registerHandler:196] 41300:32616 - JSBridge Registered handler: "hideAllToolbars"
2025-08-02 09:52:50.404 HL-Whiteboard-Qt debug [JSBridge::registerHandler:196] 41300:32616 - JSBridge Registered handler: "showAllToolbars"
2025-08-02 09:52:50.405 HL-Whiteboard-Qt debug [JSBridge::registerHandler:196] 41300:32616 - JSBridge Registered handler: "qt.getCurrentResourceTraceList"
2025-08-02 09:52:50.405 HL-Whiteboard-Qt debug [JSBridge::registerHandler:196] 41300:32616 - JSBridge Registered handler: "qt.getTraceImageList"
2025-08-02 09:52:50.405 HL-Whiteboard-Qt debug [JSBridge::registerHandler:196] 41300:32616 - JSBridge Registered handler: "classroom.exit"
2025-08-02 09:52:50.405 HL-Whiteboard-Qt debug [JSBridge::registerHandler:196] 41300:32616 - JSBridge Registered handler: "qt.openClassroomSettingDialog"
2025-08-02 09:52:50.405 HL-Whiteboard-Qt debug [WhiteboardView::WhiteboardView:99] 41300:32616 - WhiteboardView初始化完成
2025-08-02 09:52:50.406 HL-Whiteboard-Qt debug [ScreenUtils::getScreenSize:15] 41300:32616 - 【ScreenUtils】返回屏幕尺寸: QSize(1536, 864)
2025-08-02 09:52:50.406 HL-Whiteboard-Qt debug [WhiteboardView::setupComponentZOrder:1636] 41300:32616 - WhiteboardView: 通过ZIndexManager应用组件Z-Order
2025-08-02 09:52:50.407 HL-Whiteboard-Qt debug [WhiteboardView::ensureUIComponentsOnTop:1715] 41300:32616 - WhiteboardView: 通过ZIndexManager确保UI组件在顶层
2025-08-02 09:52:50.431 HL-Whiteboard-Qt info [ZmqServer::start:38] 41300:32616 - starting server:  "ipc://C:/Users/<USER>/.hl-ipc-hl-white-board"
2025-08-02 09:52:50.431 HL-Whiteboard-Qt info [ZmqServer::registerInitHandler:67] 41300:32616 - register init handler
2025-08-02 09:52:50.431 HL-Whiteboard-Qt info [ZmqServer::registerHandler:287] 41300:32616 - ZmqServer registerHandler:  "init"
2025-08-02 09:52:50.445 HL-Whiteboard-Qt info [ZmqServer::start:47] 41300:32616 - server started:  "ipc://C:/Users/<USER>/.hl-ipc-hl-white-board"
2025-08-02 09:52:50.445 HL-Whiteboard-Qt debug [JSBridge::registerHandler:196] 41300:32616 - JSBridge Registered handler: "captureScreen"
2025-08-02 09:52:50.445 HL-Whiteboard-Qt debug [JSBridge::registerHandler:196] 41300:32616 - JSBridge Registered handler: "enterFullScreen"
2025-08-02 09:52:50.445 HL-Whiteboard-Qt debug [JSBridge::registerHandler:196] 41300:32616 - JSBridge Registered handler: "exitFullScreen"
2025-08-02 09:52:50.445 HL-Whiteboard-Qt debug [JSBridge::registerHandler:196] 41300:32616 - JSBridge Registered handler: "showFullscreenButton"
2025-08-02 09:52:50.445 HL-Whiteboard-Qt debug [JSBridge::registerHandler:196] 41300:32616 - JSBridge Registered handler: "hideFullscreenButton"
2025-08-02 09:52:50.445 HL-Whiteboard-Qt debug [JSBridge::registerHandler:196] 41300:32616 - JSBridge Registered handler: "showDevTools"
2025-08-02 09:52:50.446 HL-Whiteboard-Qt debug [JSBridge::registerHandler:196] 41300:32616 - JSBridge Registered handler: "reload"
2025-08-02 09:52:50.446 HL-Whiteboard-Qt debug [JSBridge::registerHandler:196] 41300:32616 - JSBridge Registered handler: "close"
2025-08-02 09:52:50.446 HL-Whiteboard-Qt debug [JSBridge::registerHandler:196] 41300:32616 - JSBridge Registered handler: "hide"
2025-08-02 09:52:50.446 HL-Whiteboard-Qt debug [JSBridge::registerHandler:196] 41300:32616 - JSBridge Registered handler: "show"
2025-08-02 09:52:50.446 HL-Whiteboard-Qt debug [JSBridge::registerHandler:196] 41300:32616 - JSBridge Registered handler: "callElectronRenderer"
2025-08-02 09:52:50.446 HL-Whiteboard-Qt debug [JSBridge::registerHandler:196] 41300:32616 - JSBridge Registered handler: "callElectron"
2025-08-02 09:52:50.446 HL-Whiteboard-Qt debug [JSBridge::registerAsyncHandler:203] 41300:32616 - JSBridge Registered async handler: "saveFileChunk"
2025-08-02 09:52:50.446 HL-Whiteboard-Qt debug [JSBridge::registerHandler:196] 41300:32616 - JSBridge Registered handler: "showSaveFileDialog"
2025-08-02 09:52:50.446 HL-Whiteboard-Qt debug [JSBridge::registerHandler:196] 41300:32616 - JSBridge Registered handler: "toast"
2025-08-02 09:52:50.447 HL-Whiteboard-Qt debug [JSBridge::registerHandler:196] 41300:32616 - JSBridge Registered handler: "getWindowsApps"
2025-08-02 09:52:50.447 HL-Whiteboard-Qt info [ZmqServer::registerHandler:287] 41300:32616 - ZmqServer registerHandler:  "callQtCef"
2025-08-02 09:52:50.447 HL-Whiteboard-Qt info [ZmqServer::registerHandler:287] 41300:32616 - ZmqServer registerHandler:  "win-restore"
2025-08-02 09:52:50.447 HL-Whiteboard-Qt info [ZmqServer::registerHandler:287] 41300:32616 - ZmqServer registerHandler:  "win-minimize"
2025-08-02 09:52:50.447 HL-Whiteboard-Qt info [ZmqServer::registerHandler:287] 41300:32616 - ZmqServer registerHandler:  "app-exit"
2025-08-02 09:52:50.447 HL-Whiteboard-Qt info [ZmqServer::registerHandler:287] 41300:32616 - ZmqServer registerHandler:  "toast"
2025-08-02 09:52:50.447 HL-Whiteboard-Qt debug [main:213] 41300:32616 - [MAIN] 主窗口创建完成
2025-08-02 09:52:50.452 HL-Whiteboard-Qt debug [WhiteboardView::connectSignalsAndSlots::::operator():1544] 41300:32616 - "WhiteboardView: 画笔类型改变: 0"
2025-08-02 09:52:50.455 HL-Whiteboard-Qt debug [ScreenUtils::getScreenSize:15] 41300:32616 - 【ScreenUtils】返回屏幕尺寸: QSize(1536, 864)
2025-08-02 09:52:50.468 HL-Whiteboard-Qt info [BaseZmqMonitor::on_monitor_started:122] 41300:50928 - on_event Monitor started
2025-08-02 09:52:50.559 HL-Whiteboard-Qt debug [FloatMenuWidget::{ctor}::::operator():104] 41300:32616 - FloatMenuWidget: 延迟发送默认套索工具类型信号
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\wlanapi.dll'.
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\mobilenetworking.dll'.
Unloaded 'C:\Windows\System32\mobilenetworking.dll'.
Unloaded 'C:\Windows\System32\wlanapi.dll'.
2025-08-02 09:52:52.427 HL-Whiteboard-Qt debug [FloatMenuWidget::mousePressEvent:1089] 41300:32616 - FloatMenuWidget: 设置拖拽起始状态，位置: QPointF(176,212) 全局位置: QPoint(196,652)
2025-08-02 09:52:52.537 HL-Whiteboard-Qt debug [FloatMenuWidget::mouseReleaseEvent:1178] 41300:32616 - FloatMenuWidget: 检测到无效拖拽（未经过移动），重置状态
2025-08-02 09:52:53.376 HL-Whiteboard-Qt debug [WhiteboardView::connectSignalsAndSlots::::operator():1551] 41300:32616 - "WhiteboardView: 选中工具改变: pen"
2025-08-02 09:52:53.377 HL-Whiteboard-Qt debug [ArcContentManager::showArcContent:118] 41300:32616 - ArcContentManager: 显示arc内容 "pen" 位置: QPoint(-24,16) 尺寸: QSize(403, 403) 父widget尺寸: QSize(585, 355)
2025-08-02 09:52:53.377 HL-Whiteboard-Qt debug [WhiteboardView::onFloatMenuToolSelected:608] 41300:32616 - "WhiteboardView: 浮动菜单选择工具: pen"
2025-08-02 09:52:53.802 HL-Whiteboard-Qt debug [WhiteBoardWidget::handleTouchEvent:843] 41300:32616 - [MULTITOUCH] 鼠标正在绘制，忽略触摸事件
2025-08-02 09:52:54.954 HL-Whiteboard-Qt info [ZmqServer::sendRequest:144] 41300:32616 - sending identity:  "black-board-client" , msg:  {"data":{"rendererMethod":"qt.resourceTraceChange"},"id":"96674486-1bd7-4870-ace2-20f12b1262e9","method":"callElectronRenderer","requestMethod":"","status":""}
2025-08-02 09:52:56.871 HL-Whiteboard-Qt debug [PenArcContent::hitTestSvgElement:364] 41300:32616 - PenArcContent点击命中: id= "big" , scenePos= QPointF(99.2,168.8) , adjustedPos= QPointF(79.2,131.8) , svgPos= QPointF(152.669,288.75) , bounds= QRectF(126.958,266.376 59.9342x59.9342)
2025-08-02 09:52:56.871 HL-Whiteboard-Qt debug [PenArcContent::mousePressEvent:169] 41300:32616 - PenArcContent点击命中: id= "big" , 类型= "笔粗"
2025-08-02 09:52:57.385 HL-Whiteboard-Qt debug [WhiteBoardWidget::handleTouchEvent:843] 41300:32616 - [MULTITOUCH] 鼠标正在绘制，忽略触摸事件
2025-08-02 09:52:58.391 HL-Whiteboard-Qt info [ZmqServer::sendRequest:144] 41300:32616 - sending identity:  "black-board-client" , msg:  {"data":{"rendererMethod":"qt.resourceTraceChange"},"id":"f80ced2a-828d-4d51-b69c-3427e2ff27f7","method":"callElectronRenderer","requestMethod":"","status":""}
2025-08-02 09:52:59.606 HL-Whiteboard-Qt debug [WhiteboardView::connectSignalsAndSlots::::operator():1551] 41300:32616 - "WhiteboardView: 选中工具改变: passThrough"
2025-08-02 09:52:59.607 HL-Whiteboard-Qt debug [WhiteboardView::onFloatMenuToolSelected:608] 41300:32616 - "WhiteboardView: 浮动菜单选择工具: passThrough"
2025-08-02 09:52:59.628 HL-Whiteboard-Qt debug [MainWindow::onCurrentToolChanged:153] 41300:32616 - 工具切换： 15 穿透模式： 启用
hl-whiteboard-qt.exe (41300): Loaded 'C:\Windows\System32\OneCoreCommonProxyStub.dll'.
