#ifndef VELOCITYDRAWINGSTATE_H
#define VELOCITYDRAWINGSTATE_H

#include <QPointF>
#include <QPainterPath>
#include <QPen>
#include <QBrush>
#include <QRectF>
#include <QElapsedTimer>
#include <QVector>
#include "../core/WhiteBoardTypes.h"

/**
 * @brief 速度数据结构
 */
struct VelocityData {
    QPointF position;
    qreal velocity;        // 像素/秒
    qint64 timestamp;      // 毫秒
    qreal calculatedWidth; // 基于速度计算的宽度
    
    VelocityData() : velocity(0), timestamp(0), calculatedWidth(0) {}
    VelocityData(const QPointF& pos, qreal vel, qint64 time, qreal width)
        : position(pos), velocity(vel), timestamp(time), calculatedWidth(width) {}
};

/**
 * @brief 速度感应绘制状态类
 * 
 * 核心功能：
 * 1. 根据绘制速度动态调整笔刷宽度
 * 2. 慢速绘制 → 粗线条
 * 3. 快速绘制 → 细线条
 * 4. 平滑的宽度过渡
 */
class VelocityDrawingState
{
public:
    VelocityDrawingState();
    ~VelocityDrawingState() = default;

    // 基本属性设置
    void setToolType(ToolType toolType) { m_toolType = toolType; }
    void setPen(const QPen& pen);
    void setBrush(const QBrush& brush) { m_brush = brush; }
    
    // 属性获取
    ToolType getToolType() const { return m_toolType; }
    QPen getPen() const { return m_pen; }
    QBrush getBrush() const { return m_brush; }
    qreal getBaseWidth() const { return m_baseWidth; }

    // 绘制操作
    void startDrawing(const QPointF& startPoint);
    void continueDrawing(const QPointF& point);
    void finishDrawing();
    void cancelDrawing();

    // 路径获取
    QPainterPath getCurrentPath() const;
    QPainterPath getVelocityAdjustedPath() const;
    
    // 状态查询
    bool isDrawing() const { return m_isDrawing; }
    bool hasVelocityData() const { return !m_velocityHistory.isEmpty(); }
    
    // 速度感应配置
    void setVelocityEnabled(bool enabled) { m_velocityEnabled = enabled; }
    void setVelocitySettings(qreal minWidthRatio, qreal maxWidthRatio, qreal velocityThreshold);
    void setSmoothingFactor(qreal factor) { m_smoothingFactor = qBound(0.1, factor, 0.9); }
    
    bool isVelocityEnabled() const { return m_velocityEnabled; }
    qreal getMinWidthRatio() const { return m_minWidthRatio; }
    qreal getMaxWidthRatio() const { return m_maxWidthRatio; }
    qreal getVelocityThreshold() const { return m_velocityThreshold; }
    
    // 调试和分析
    QVector<VelocityData> getVelocityHistory() const { return m_velocityHistory; }
    qreal getCurrentVelocity() const;
    qreal getCurrentCalculatedWidth() const;

private:
    // 基本状态
    ToolType m_toolType = ToolType::FreeDraw;
    QPen m_pen;
    QBrush m_brush;
    qreal m_baseWidth = 5.0;
    bool m_isDrawing = false;
    
    // 点位信息
    QPointF m_startPoint;
    QPointF m_currentPoint;
    QPointF m_lastPoint;
    
    // 速度感应相关
    bool m_velocityEnabled = true;
    QVector<VelocityData> m_velocityHistory;
    QElapsedTimer m_velocityTimer;
    
    // 速度配置参数
    qreal m_minWidthRatio = 0.3;      // 最小宽度比例（快速时）
    qreal m_maxWidthRatio = 1.5;      // 最大宽度比例（慢速时）
    qreal m_velocityThreshold = 200.0; // 速度阈值（像素/秒）
    qreal m_smoothingFactor = 0.3;     // 平滑因子（0.1-0.9）
    
    // 路径构建
    QVector<QPointF> m_pathPoints;
    
    // 内部方法
    qreal calculateVelocity(const QPointF& currentPoint, qint64 currentTime);
    qreal calculateWidthFromVelocity(qreal velocity);
    qreal applyVelocitySmoothing(qreal currentVelocity);
    void updateVelocityData(const QPointF& point, qint64 timestamp);
    QPainterPath buildVariableWidthPath() const;
    void addVariableWidthSegment(QPainterPath& path, const QPointF& p1, const QPointF& p2, 
                                qreal width1, qreal width2) const;
    
    // 常量
    static constexpr int MAX_VELOCITY_HISTORY = 20;
    static constexpr qreal MIN_TIME_DIFF = 1.0; // 最小时间差（毫秒）
    static constexpr qreal MIN_DISTANCE = 0.5;  // 最小距离（像素）
};

#endif // VELOCITYDRAWINGSTATE_H
