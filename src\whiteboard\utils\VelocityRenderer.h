#ifndef VELOCITYRENDERER_H
#define VELOCITYRENDERER_H

#include <QPainter>
#include <QPen>
#include <QBrush>
#include <QPainterPath>
#include <QVector>
#include "../core/WhiteBoardTypes.h"
#include "../optimization/VelocityDrawingState.h"

/**
 * @brief 速度感应渲染器
 * 
 * 核心功能：
 * 1. 渲染基于速度的变宽度笔刷
 * 2. 支持实时绘制和历史绘制
 * 3. 提供多种渲染模式
 */
class VelocityRenderer
{
public:
    /**
     * @brief 渲染模式枚举
     */
    enum class RenderMode {
        VariableWidth,    // 变宽度路径模式
        MultiStroke,      // 多笔画叠加模式
        TextureBrush      // 纹理笔刷模式
    };

    VelocityRenderer() = default;
    ~VelocityRenderer() = default;

    /**
     * @brief 使用速度数据渲染路径
     * @param painter 绘制器
     * @param velocityState 速度绘制状态
     * @param mode 渲染模式
     */
    static void renderVelocityPath(QPainter* painter, const VelocityDrawingState& velocityState, 
                                 RenderMode mode = RenderMode::VariableWidth);

    /**
     * @brief 渲染变宽度路径
     * @param painter 绘制器
     * @param velocityData 速度数据数组
     * @param pen 基础画笔
     * @param brush 画刷
     */
    static void renderVariableWidthPath(QPainter* painter, const QVector<VelocityData>& velocityData,
                                      const QPen& pen, const QBrush& brush);

    /**
     * @brief 渲染多笔画叠加效果
     * @param painter 绘制器
     * @param velocityData 速度数据数组
     * @param pen 基础画笔
     */
    static void renderMultiStrokePath(QPainter* painter, const QVector<VelocityData>& velocityData,
                                    const QPen& pen);

    /**
     * @brief 渲染纹理笔刷效果
     * @param painter 绘制器
     * @param velocityData 速度数据数组
     * @param pen 基础画笔
     */
    static void renderTextureBrushPath(QPainter* painter, const QVector<VelocityData>& velocityData,
                                     const QPen& pen);

    /**
     * @brief 设置渲染质量
     * @param painter 绘制器
     * @param highQuality 是否高质量渲染
     */
    static void setupRenderingHints(QPainter* painter, bool highQuality = true);

private:
    /**
     * @brief 创建变宽度线段
     * @param p1 起始点
     * @param p2 结束点
     * @param width1 起始宽度
     * @param width2 结束宽度
     * @return 四边形路径
     */
    static QPainterPath createVariableWidthSegment(const QPointF& p1, const QPointF& p2,
                                                  qreal width1, qreal width2);

    /**
     * @brief 创建圆形笔刷纹理
     * @param size 纹理大小
     * @param color 颜色
     * @return 纹理像素图
     */
    static QPixmap createCircularBrushTexture(int size, const QColor& color);

    /**
     * @brief 计算两点间的插值点
     * @param p1 起始点
     * @param p2 结束点
     * @param steps 插值步数
     * @return 插值点数组
     */
    static QVector<QPointF> interpolatePoints(const QPointF& p1, const QPointF& p2, int steps);

    // 缓存的纹理
    static QMap<QString, QPixmap> s_textureCache;
};

#endif // VELOCITYRENDERER_H
