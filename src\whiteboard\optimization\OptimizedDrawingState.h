#ifndef OPTIMIZEDDRAWINGSTATE_H
#define OPTIMIZEDDRAWINGSTATE_H

#include <QPointF>
#include <QPainterPath>
#include <QPen>
#include <QBrush>
#include <QRectF>
#include <QElapsedTimer>
#include <QVector>
#include "IncrementalPathBuilder.h"
#include "DirtyRegionManager.h"
#include "../core/WhiteBoardTypes.h"

/**
 * @brief 速度数据结构
 */
struct VelocityData {
    QPointF position;
    qreal velocity;        // 像素/秒
    qint64 timestamp;      // 毫秒
    qreal calculatedWidth; // 基于速度计算的宽度

    VelocityData() : velocity(0), timestamp(0), calculatedWidth(0) {}
    VelocityData(const QPointF& pos, qreal vel, qint64 time, qreal width)
        : position(pos), velocity(vel), timestamp(time), calculatedWidth(width) {}
};

/**
 * @brief 优化的绘制状态管理器
 *
 * 核心功能：
 * 1. 集成增量路径构建和脏区域管理
 * 2. 提供统一的绘制状态接口
 * 3. 优化不同工具类型的绘制性能
 * 4. 速度感应笔锋支持
 */
class OptimizedDrawingState
{
public:
    OptimizedDrawingState();
    ~OptimizedDrawingState() = default;

    // 基本属性
    void setToolType(ToolType toolType);
    void setPen(const QPen& pen);
    void setBrush(const QBrush& brush);
    
    ToolType getToolType() const { return m_toolType; }
    QPen getPen() const { return m_pen; }
    QBrush getBrush() const { return m_brush; }

    // 绘制操作
    void startDrawing(const QPointF& startPoint);
    void continueDrawing(const QPointF& point);
    void finishDrawing();
    void cancelDrawing();

    // 路径获取
    QPainterPath getCurrentPath() const;

    QRectF getCurrentBounds() const;
    
    // 脏区域管理
    QRectF getDirtyRegion();  // 获取增量脏区域
    bool hasDirtyRegions() const;
    void clearDirtyRegions();

    // 状态查询
    bool isDrawing() const { return m_isDrawing; }
    bool hasPath() const;
    bool needsUpdate() const;

    // 点位信息获取
    QPointF getCurrentPoint() const { return m_currentPoint; }
    QPointF getStartPoint() const { return m_startPoint; }
    QPointF getLastPoint() const { return m_lastPoint; }
    
    // 性能优化配置
    void setBatchSize(int size);

    // 速度感应功能
    void setVelocityEnabled(bool enabled) { m_velocityEnabled = enabled; }
    bool isVelocityEnabled() const { return m_velocityEnabled; }
    void setVelocitySettings(qreal minWidthRatio, qreal maxWidthRatio, qreal velocityThreshold);
    QPainterPath getVelocityAdjustedPath() const;
    QVector<VelocityData> getVelocityHistory() const { return m_velocityHistory; }
    qreal getCurrentVelocity() const;
    qreal getCurrentCalculatedWidth() const;





private:
    // 基本状态
    ToolType m_toolType = ToolType::FreeDraw;
    QPen m_pen;
    QBrush m_brush;
    bool m_isDrawing = false;
    
    // 点位信息
    QPointF m_startPoint;
    QPointF m_currentPoint;
    QPointF m_lastPoint;
    
    // 优化组件
    IncrementalPathBuilder m_pathBuilder;
    DirtyRegionManager m_dirtyRegionManager;



    // 双缓冲脏区域管理
    QRectF m_lastDrawnRegion;      // 上一次绘制的区域
    QRectF m_currentDrawRegion;    // 当前绘制的区域

    // 配置
    bool m_dirtyRegionOptimizationEnabled = true;

    // 速度感应相关
    bool m_velocityEnabled = false;
    QVector<VelocityData> m_velocityHistory;
    QElapsedTimer m_velocityTimer;
    qreal m_baseWidth = 5.0;
    qreal m_minWidthRatio = 0.3;      // 最小宽度比例（快速时）
    qreal m_maxWidthRatio = 1.5;      // 最大宽度比例（慢速时）
    qreal m_velocityThreshold = 200.0; // 速度阈值（像素/秒）
    qreal m_smoothingFactor = 0.3;     // 平滑因子

    // 最小脏区域设置
    static constexpr qreal MIN_DIRTY_REGION_SIZE = 35.0;  // 最小脏区域尺寸（确保微小图形可见）
    
    // 内部方法
    void updateDirtyRegion(const QPointF& newPoint);
    void handleToolSpecificDrawing(const QPointF& point);
    void handleShapeToolDrawing(const QPointF& point, qreal margin);
    QRectF calculateShapeRegion(const QPointF& point, qreal margin);
    QRectF ensureMinimumRegionSize(const QRectF& region) const;

    // 速度感应内部方法
    qreal calculateVelocity(const QPointF& currentPoint, qint64 currentTime);
    qreal calculateWidthFromVelocity(qreal velocity);
    qreal applyVelocitySmoothing(qreal currentVelocity);
    void updateVelocityData(const QPointF& point, qint64 timestamp);
    QPainterPath buildVariableWidthPath() const;
    void addVariableWidthSegment(QPainterPath& path, const QPointF& p1, const QPointF& p2,
                                qreal width1, qreal width2) const;

    // 常量
    static constexpr int MAX_VELOCITY_HISTORY = 20;
    static constexpr qreal MIN_TIME_DIFF = 1.0; // 最小时间差（毫秒）
    static constexpr qreal MIN_DISTANCE = 0.5;  // 最小距离（像素）


};

#endif // OPTIMIZEDDRAWINGSTATE_H
