#include "VelocityDrawingState.h"
#include <QtMath>
#include <QDebug>

VelocityDrawingState::VelocityDrawingState()
    : m_toolType(ToolType::FreeDraw)
    , m_baseWidth(5.0)
    , m_isDrawing(false)
    , m_velocityEnabled(true)
    , m_minWidthRatio(0.3)
    , m_maxWidthRatio(1.5)
    , m_velocityThreshold(200.0)
    , m_smoothingFactor(0.3)
{
    // 初始化画笔
    m_pen = QPen(Qt::black, m_baseWidth, Qt::SolidLine, Qt::RoundCap, Qt::RoundJoin);
}

void VelocityDrawingState::setPen(const QPen& pen)
{
    m_pen = pen;
    m_baseWidth = pen.widthF();
}

void VelocityDrawingState::setVelocitySettings(qreal minWidthRatio, qreal maxWidthRatio, qreal velocityThreshold)
{
    m_minWidthRatio = qBound(0.1, minWidthRatio, 1.0);
    m_maxWidthRatio = qBound(1.0, maxWidthRatio, 3.0);
    m_velocityThreshold = qMax(50.0, velocityThreshold);
}

void VelocityDrawingState::startDrawing(const QPointF& startPoint)
{
    m_isDrawing = true;
    m_startPoint = startPoint;
    m_currentPoint = startPoint;
    m_lastPoint = startPoint;
    
    // 清空历史数据
    m_velocityHistory.clear();
    m_pathPoints.clear();
    
    // 启动计时器
    m_velocityTimer.start();
    
    // 添加起始点
    m_pathPoints.append(startPoint);
    
    // 添加初始速度数据
    VelocityData initialData(startPoint, 0.0, 0, m_baseWidth);
    m_velocityHistory.append(initialData);
}

void VelocityDrawingState::continueDrawing(const QPointF& point)
{
    if (!m_isDrawing) {
        return;
    }
    
    qint64 currentTime = m_velocityTimer.elapsed();
    
    // 计算与上一个点的距离
    qreal distance = QLineF(m_currentPoint, point).length();
    
    // 如果距离太小，跳过这个点以避免噪声
    if (distance < MIN_DISTANCE) {
        return;
    }
    
    // 更新点位信息
    m_lastPoint = m_currentPoint;
    m_currentPoint = point;
    
    // 添加到路径点
    m_pathPoints.append(point);
    
    // 更新速度数据
    updateVelocityData(point, currentTime);
}

void VelocityDrawingState::finishDrawing()
{
    m_isDrawing = false;
}

void VelocityDrawingState::cancelDrawing()
{
    m_isDrawing = false;
    m_velocityHistory.clear();
    m_pathPoints.clear();
}

QPainterPath VelocityDrawingState::getCurrentPath() const
{
    if (m_pathPoints.size() < 2) {
        return QPainterPath();
    }
    
    QPainterPath path;
    path.moveTo(m_pathPoints.first());
    
    for (int i = 1; i < m_pathPoints.size(); ++i) {
        path.lineTo(m_pathPoints[i]);
    }
    
    return path;
}

QPainterPath VelocityDrawingState::getVelocityAdjustedPath() const
{
    if (!m_velocityEnabled || m_velocityHistory.size() < 2) {
        return getCurrentPath();
    }
    
    return buildVariableWidthPath();
}

qreal VelocityDrawingState::getCurrentVelocity() const
{
    if (m_velocityHistory.isEmpty()) {
        return 0.0;
    }
    return m_velocityHistory.last().velocity;
}

qreal VelocityDrawingState::getCurrentCalculatedWidth() const
{
    if (m_velocityHistory.isEmpty()) {
        return m_baseWidth;
    }
    return m_velocityHistory.last().calculatedWidth;
}

void VelocityDrawingState::updateVelocityData(const QPointF& point, qint64 timestamp)
{
    qreal velocity = calculateVelocity(point, timestamp);
    
    // 应用平滑
    velocity = applyVelocitySmoothing(velocity);
    
    // 计算基于速度的宽度
    qreal calculatedWidth = calculateWidthFromVelocity(velocity);
    
    // 创建速度数据
    VelocityData data(point, velocity, timestamp, calculatedWidth);
    m_velocityHistory.append(data);
    
    // 限制历史记录大小
    if (m_velocityHistory.size() > MAX_VELOCITY_HISTORY) {
        m_velocityHistory.removeFirst();
    }
}

qreal VelocityDrawingState::calculateVelocity(const QPointF& currentPoint, qint64 currentTime)
{
    if (m_velocityHistory.isEmpty()) {
        return 0.0;
    }
    
    const VelocityData& lastData = m_velocityHistory.last();
    qreal timeDiff = currentTime - lastData.timestamp;
    
    // 避免除零和时间差太小的情况
    if (timeDiff < MIN_TIME_DIFF) {
        return lastData.velocity;
    }
    
    qreal distance = QLineF(lastData.position, currentPoint).length();
    qreal velocity = (distance / timeDiff) * 1000.0; // 转换为像素/秒
    
    return velocity;
}

qreal VelocityDrawingState::calculateWidthFromVelocity(qreal velocity)
{
    if (velocity <= 0) {
        return m_baseWidth * m_maxWidthRatio;
    }
    
    // 使用指数衰减函数，提供更自然的宽度变化
    qreal normalizedVelocity = velocity / m_velocityThreshold;
    
    // 限制最大归一化速度
    normalizedVelocity = qMin(normalizedVelocity, 2.0);
    
    // 使用平滑的指数衰减
    qreal widthRatio = m_minWidthRatio + (m_maxWidthRatio - m_minWidthRatio) * qExp(-normalizedVelocity);
    
    return m_baseWidth * widthRatio;
}

qreal VelocityDrawingState::applyVelocitySmoothing(qreal currentVelocity)
{
    if (m_velocityHistory.size() < 2) {
        return currentVelocity;
    }
    
    // 使用指数移动平均进行平滑
    qreal smoothedVelocity = currentVelocity;
    
    // 取最近几个速度值进行平滑
    int smoothingWindow = qMin(5, m_velocityHistory.size());
    qreal weightSum = 0.0;
    qreal velocitySum = 0.0;
    
    for (int i = 0; i < smoothingWindow; ++i) {
        int index = m_velocityHistory.size() - 1 - i;
        qreal weight = qPow(m_smoothingFactor, i);
        velocitySum += m_velocityHistory[index].velocity * weight;
        weightSum += weight;
    }
    
    if (weightSum > 0) {
        smoothedVelocity = (velocitySum + currentVelocity * (1.0 - m_smoothingFactor)) / (weightSum + (1.0 - m_smoothingFactor));
    }
    
    return smoothedVelocity;
}
