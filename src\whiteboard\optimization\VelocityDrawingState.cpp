#include "VelocityDrawingState.h"
#include <QtMath>
#include <QDebug>

VelocityDrawingState::VelocityDrawingState()
    : m_toolType(ToolType::FreeDraw)
    , m_baseWidth(5.0)
    , m_isDrawing(false)
    , m_velocityEnabled(true)
    , m_minWidthRatio(0.3)
    , m_maxWidthRatio(1.5)
    , m_velocityThreshold(200.0)
    , m_smoothingFactor(0.3)
{
    // 初始化画笔
    m_pen = QPen(Qt::black, m_baseWidth, Qt::SolidLine, Qt::RoundCap, Qt::RoundJoin);
}

void VelocityDrawingState::setPen(const QPen& pen)
{
    m_pen = pen;
    m_baseWidth = pen.widthF();
}

void VelocityDrawingState::setVelocitySettings(qreal minWidthRatio, qreal maxWidthRatio, qreal velocityThreshold)
{
    m_minWidthRatio = qBound(0.1, minWidthRatio, 1.0);
    m_maxWidthRatio = qBound(1.0, maxWidthRatio, 3.0);
    m_velocityThreshold = qMax(50.0, velocityThreshold);
}

void VelocityDrawingState::startDrawing(const QPointF& startPoint)
{
    m_isDrawing = true;
    m_startPoint = startPoint;
    m_currentPoint = startPoint;
    m_lastPoint = startPoint;
    
    // 清空历史数据
    m_velocityHistory.clear();
    m_pathPoints.clear();
    
    // 启动计时器
    m_velocityTimer.start();
    
    // 添加起始点
    m_pathPoints.append(startPoint);
    
    // 添加初始速度数据
    VelocityData initialData(startPoint, 0.0, 0, m_baseWidth);
    m_velocityHistory.append(initialData);
}

void VelocityDrawingState::continueDrawing(const QPointF& point)
{
    if (!m_isDrawing) {
        return;
    }
    
    qint64 currentTime = m_velocityTimer.elapsed();
    
    // 计算与上一个点的距离
    qreal distance = QLineF(m_currentPoint, point).length();
    
    // 如果距离太小，跳过这个点以避免噪声
    if (distance < MIN_DISTANCE) {
        return;
    }
    
    // 更新点位信息
    m_lastPoint = m_currentPoint;
    m_currentPoint = point;
    
    // 添加到路径点
    m_pathPoints.append(point);
    
    // 更新速度数据
    updateVelocityData(point, currentTime);
}

void VelocityDrawingState::finishDrawing()
{
    m_isDrawing = false;
}

void VelocityDrawingState::cancelDrawing()
{
    m_isDrawing = false;
    m_velocityHistory.clear();
    m_pathPoints.clear();
}

QPainterPath VelocityDrawingState::getCurrentPath() const
{
    if (m_pathPoints.size() < 2) {
        return QPainterPath();
    }
    
    QPainterPath path;
    path.moveTo(m_pathPoints.first());
    
    for (int i = 1; i < m_pathPoints.size(); ++i) {
        path.lineTo(m_pathPoints[i]);
    }
    
    return path;
}

QPainterPath VelocityDrawingState::getVelocityAdjustedPath() const
{
    if (!m_velocityEnabled || m_velocityHistory.size() < 2) {
        return getCurrentPath();
    }
    
    return buildVariableWidthPath();
}

qreal VelocityDrawingState::getCurrentVelocity() const
{
    if (m_velocityHistory.isEmpty()) {
        return 0.0;
    }
    return m_velocityHistory.last().velocity;
}

qreal VelocityDrawingState::getCurrentCalculatedWidth() const
{
    if (m_velocityHistory.isEmpty()) {
        return m_baseWidth;
    }
    return m_velocityHistory.last().calculatedWidth;
}

void VelocityDrawingState::updateVelocityData(const QPointF& point, qint64 timestamp)
{
    qreal velocity = calculateVelocity(point, timestamp);
    
    // 应用平滑
    velocity = applyVelocitySmoothing(velocity);
    
    // 计算基于速度的宽度
    qreal calculatedWidth = calculateWidthFromVelocity(velocity);
    
    // 创建速度数据
    VelocityData data(point, velocity, timestamp, calculatedWidth);
    m_velocityHistory.append(data);
    
    // 限制历史记录大小
    if (m_velocityHistory.size() > MAX_VELOCITY_HISTORY) {
        m_velocityHistory.removeFirst();
    }
}

qreal VelocityDrawingState::calculateVelocity(const QPointF& currentPoint, qint64 currentTime)
{
    if (m_velocityHistory.isEmpty()) {
        return 0.0;
    }
    
    const VelocityData& lastData = m_velocityHistory.last();
    qreal timeDiff = currentTime - lastData.timestamp;
    
    // 避免除零和时间差太小的情况
    if (timeDiff < MIN_TIME_DIFF) {
        return lastData.velocity;
    }
    
    qreal distance = QLineF(lastData.position, currentPoint).length();
    qreal velocity = (distance / timeDiff) * 1000.0; // 转换为像素/秒
    
    return velocity;
}

qreal VelocityDrawingState::calculateWidthFromVelocity(qreal velocity)
{
    if (velocity <= 0) {
        return m_baseWidth * m_maxWidthRatio;
    }
    
    // 使用指数衰减函数，提供更自然的宽度变化
    qreal normalizedVelocity = velocity / m_velocityThreshold;
    
    // 限制最大归一化速度
    normalizedVelocity = qMin(normalizedVelocity, 2.0);
    
    // 使用平滑的指数衰减
    qreal widthRatio = m_minWidthRatio + (m_maxWidthRatio - m_minWidthRatio) * qExp(-normalizedVelocity);
    
    return m_baseWidth * widthRatio;
}

qreal VelocityDrawingState::applyVelocitySmoothing(qreal currentVelocity)
{
    if (m_velocityHistory.size() < 2) {
        return currentVelocity;
    }
    
    // 使用指数移动平均进行平滑
    qreal smoothedVelocity = currentVelocity;
    
    // 取最近几个速度值进行平滑
    int smoothingWindow = qMin(5, m_velocityHistory.size());
    qreal weightSum = 0.0;
    qreal velocitySum = 0.0;
    
    for (int i = 0; i < smoothingWindow; ++i) {
        int index = m_velocityHistory.size() - 1 - i;
        qreal weight = qPow(m_smoothingFactor, i);
        velocitySum += m_velocityHistory[index].velocity * weight;
        weightSum += weight;
    }
    
    if (weightSum > 0) {
        smoothedVelocity = (velocitySum + currentVelocity * (1.0 - m_smoothingFactor)) / (weightSum + (1.0 - m_smoothingFactor));
    }
    
    return smoothedVelocity;
}

QPainterPath VelocityDrawingState::buildVariableWidthPath() const
{
    if (m_velocityHistory.size() < 2) {
        return QPainterPath();
    }

    QPainterPath path;

    // 为每个线段创建变宽度的四边形
    for (int i = 0; i < m_velocityHistory.size() - 1; ++i) {
        const VelocityData& data1 = m_velocityHistory[i];
        const VelocityData& data2 = m_velocityHistory[i + 1];

        addVariableWidthSegment(path, data1.position, data2.position,
                              data1.calculatedWidth, data2.calculatedWidth);
    }

    return path;
}

void VelocityDrawingState::addVariableWidthSegment(QPainterPath& path, const QPointF& p1, const QPointF& p2,
                                                  qreal width1, qreal width2) const
{
    // 计算线段方向向量
    QPointF direction = p2 - p1;
    qreal length = qSqrt(direction.x() * direction.x() + direction.y() * direction.y());

    if (length < 0.001) {
        return; // 避免除零
    }

    // 归一化方向向量
    direction /= length;

    // 计算垂直向量（法向量）
    QPointF normal(-direction.y(), direction.x());

    // 计算四个顶点
    QPointF p1_left = p1 + normal * (width1 * 0.5);
    QPointF p1_right = p1 - normal * (width1 * 0.5);
    QPointF p2_left = p2 + normal * (width2 * 0.5);
    QPointF p2_right = p2 - normal * (width2 * 0.5);

    // 创建四边形路径
    if (path.isEmpty()) {
        path.moveTo(p1_left);
        path.lineTo(p2_left);
        path.lineTo(p2_right);
        path.lineTo(p1_right);
        path.closeSubpath();
    } else {
        // 连接到前一个四边形
        QPainterPath segment;
        segment.moveTo(p1_left);
        segment.lineTo(p2_left);
        segment.lineTo(p2_right);
        segment.lineTo(p1_right);
        segment.closeSubpath();

        path = path.united(segment);
    }
}
