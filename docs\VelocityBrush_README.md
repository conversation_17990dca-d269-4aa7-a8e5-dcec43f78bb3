# 速度感应笔锋功能说明

## 概述

速度感应笔锋是一个基于绘制速度动态调整笔刷宽度的功能，实现了类似传统书法和绘画中的笔锋效果：
- **慢速绘制**：线条粗壮，适合强调和细节描绘
- **快速绘制**：线条纤细，适合流畅的线条和轮廓

## 核心特性

### 1. 速度计算
- 实时计算相邻点间的绘制速度（像素/秒）
- 使用指数移动平均进行速度平滑，避免抖动
- 支持可配置的速度阈值和响应曲线

### 2. 宽度调制
- 基于速度的非线性宽度映射
- 可配置的最小/最大宽度比例
- 平滑的宽度过渡，避免突变

### 3. 多种渲染模式
- **变宽度路径**：生成真实的变宽度几何路径
- **多笔画叠加**：通过多层绘制模拟笔锋效果
- **纹理笔刷**：使用渐变纹理实现柔和边缘

## 使用方法

### 基本启用
```cpp
// 创建白板组件
WhiteBoardWidget* whiteboard = new WhiteBoardWidget();

// 启用速度感应
whiteboard->setVelocityEnabled(true);

// 设置为自由绘制工具
whiteboard->setCurrentTool(ToolType::FreeDraw);
```

### 参数配置
```cpp
// 设置速度感应参数
whiteboard->setVelocitySettings(
    0.3,    // 最小宽度比例（快速时）
    1.5,    // 最大宽度比例（慢速时）
    200.0   // 速度阈值（像素/秒）
);

// 设置渲染模式
whiteboard->setVelocityRenderMode(VelocityRenderer::RenderMode::VariableWidth);
```

### 支持的工具类型
目前速度感应功能支持以下工具：
- `ToolType::FreeDraw` - 自由绘制
- `ToolType::FreeDrawHighlighter` - 荧光笔

## 参数说明

### 速度感应参数

| 参数 | 类型 | 范围 | 默认值 | 说明 |
|------|------|------|--------|------|
| minWidthRatio | qreal | 0.1-1.0 | 0.3 | 快速绘制时的最小宽度比例 |
| maxWidthRatio | qreal | 1.0-3.0 | 1.5 | 慢速绘制时的最大宽度比例 |
| velocityThreshold | qreal | 50-500 | 200 | 速度阈值（像素/秒） |

### 渲染模式

| 模式 | 特点 | 性能 | 适用场景 |
|------|------|------|----------|
| VariableWidth | 真实几何路径，精确宽度 | 中等 | 需要精确宽度控制 |
| MultiStroke | 多层叠加，柔和效果 | 较低 | 艺术绘画，柔和笔触 |
| TextureBrush | 纹理渐变，自然边缘 | 较高 | 实时绘制，自然效果 |

## 技术实现

### 核心类结构
```
VelocityDrawingState     - 速度感应绘制状态管理
├── VelocityData        - 速度数据结构
├── 速度计算算法         - 实时速度计算和平滑
└── 宽度映射函数         - 速度到宽度的映射

VelocityRenderer        - 速度感应渲染器
├── VariableWidth       - 变宽度路径渲染
├── MultiStroke         - 多笔画叠加渲染
└── TextureBrush        - 纹理笔刷渲染

WhiteBoardWidget        - 集成接口
├── 速度感应开关         - 启用/禁用控制
├── 参数配置接口         - 实时参数调整
└── 渲染模式切换         - 动态模式切换
```

### 速度计算公式
```cpp
// 基础速度计算
velocity = distance / timeDiff * 1000  // 像素/秒

// 指数移动平均平滑
smoothedVelocity = α * currentVelocity + (1-α) * previousVelocity

// 宽度映射（指数衰减）
widthRatio = minRatio + (maxRatio - minRatio) * exp(-velocity/threshold)
```

## 演示程序

运行演示程序查看效果：
```cpp
#include "examples/VelocityBrushDemo.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    VelocityBrushDemo demo;
    demo.show();
    return app.exec();
}
```

演示程序提供：
- 实时参数调整滑块
- 多种渲染模式切换
- 清空画布和重置功能
- 参数效果实时预览

## 性能优化

### 建议设置
- **实时绘制**：使用 TextureBrush 模式，性能最佳
- **精确控制**：使用 VariableWidth 模式，质量最高
- **艺术效果**：使用 MultiStroke 模式，效果最柔和

### 性能调优
```cpp
// 减少历史记录大小
VelocityDrawingState::MAX_VELOCITY_HISTORY = 10;

// 调整最小距离阈值
VelocityDrawingState::MIN_DISTANCE = 1.0;

// 优化平滑因子
velocityState.setSmoothingFactor(0.5);
```

## 注意事项

1. **设备兼容性**：在触摸设备上效果最佳，鼠标设备可能速度变化不够明显
2. **性能影响**：启用速度感应会增加一定的计算开销
3. **参数调试**：建议根据具体使用场景调整速度阈值和宽度比例
4. **工具限制**：目前仅支持自由绘制类工具，几何图形工具暂不支持

## 扩展开发

### 添加新的渲染模式
```cpp
// 在 VelocityRenderer 中添加新模式
enum class RenderMode {
    VariableWidth,
    MultiStroke,
    TextureBrush,
    CustomMode    // 新模式
};

// 实现渲染方法
static void renderCustomMode(QPainter* painter, 
                            const QVector<VelocityData>& velocityData,
                            const QPen& pen);
```

### 自定义速度映射函数
```cpp
// 在 VelocityDrawingState 中重写
qreal calculateWidthFromVelocity(qreal velocity) override {
    // 自定义映射逻辑
    return customMappingFunction(velocity);
}
```
