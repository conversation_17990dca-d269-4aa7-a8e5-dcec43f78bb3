#include "OptimizedDrawingState.h"
#include "../tools/ShapeToolManager.h"
#include "../utils/DrawingPerformanceProfiler.h"
#include <QDateTime>
#include <QtMath>

OptimizedDrawingState::OptimizedDrawingState()
{
}

void OptimizedDrawingState::setToolType(ToolType toolType)
{
    m_toolType = toolType;
    m_pathBuilder.setToolType(toolType);
}

void OptimizedDrawingState::setPen(const QPen& pen)
{
    m_pen = pen;
    m_baseWidth = pen.widthF();
    m_pathBuilder.setPen(pen);  // 传递画笔信息给路径构建器
}

void OptimizedDrawingState::setBrush(const QBrush& brush)
{
    m_brush = brush;
}

void OptimizedDrawingState::startDrawing(const QPointF& startPoint)
{
    DRAWING_TIMER("OptimizedDrawingState::startDrawing");
    m_isDrawing = true;
    m_startPoint = startPoint;
    m_currentPoint = startPoint;
    m_lastPoint = startPoint;

    // 重置双缓冲区域
    m_lastDrawnRegion = QRectF();
    m_currentDrawRegion = QRectF();

    // 启动路径构建
    m_pathBuilder.startPath(startPoint);

    // 速度感应初始化
    if (m_velocityEnabled) {
        m_velocityHistory.clear();
        m_velocityTimer.start();

        // 添加初始速度数据
        VelocityData initialData(startPoint, 0.0, 0, m_baseWidth);
        m_velocityHistory.append(initialData);
    }

    // 添加初始脏区域
    if (m_dirtyRegionOptimizationEnabled) {
        qreal penWidth = qMax(m_pen.widthF(), 2.0);
        qreal radius = qMax(penWidth + 3.0, MIN_DIRTY_REGION_SIZE / 2.0);
        m_dirtyRegionManager.addDirtyPoint(startPoint, radius);
    }
}

void OptimizedDrawingState::continueDrawing(const QPointF& point)
{
    DRAWING_TIMER("OptimizedDrawingState::continueDrawing");
    if (!m_isDrawing) {
        return;
    }

    // 速度感应处理
    if (m_velocityEnabled) {
        qint64 currentTime = m_velocityTimer.elapsed();
        qreal distance = QLineF(m_currentPoint, point).length();

        // 如果距离太小，跳过这个点以避免噪声
        if (distance >= MIN_DISTANCE) {
            m_lastPoint = m_currentPoint;
            m_currentPoint = point;

            // 更新速度数据
            updateVelocityData(point, currentTime);

            // 添加点到路径构建器
            m_pathBuilder.addPoint(point);
        }
    } else {
        m_lastPoint = m_currentPoint;
        m_currentPoint = point;

        // 添加点到路径构建器
        m_pathBuilder.addPoint(point);
    }

    // 处理工具特定的绘制逻辑（包括脏区域管理）
    handleToolSpecificDrawing(point);
}

void OptimizedDrawingState::finishDrawing()
{
    if (!m_isDrawing) {
        return;
    }

    m_isDrawing = false;



    m_pathBuilder.finishPath();

    // 清理双缓冲状态和脏区域
    if (m_dirtyRegionOptimizationEnabled) {
        m_dirtyRegionManager.clearDirtyRegions();
        m_lastDrawnRegion = QRectF();
        m_currentDrawRegion = QRectF();
    }
}

void OptimizedDrawingState::cancelDrawing()
{
    m_isDrawing = false;

    m_pathBuilder.cancelPath();
    m_dirtyRegionManager.clearDirtyRegions();

    // 清理速度感应数据
    if (m_velocityEnabled) {
        m_velocityHistory.clear();
    }
}

QPainterPath OptimizedDrawingState::getCurrentPath() const
{
    // 直接返回PathBuilder的当前路径
    return m_pathBuilder.getCurrentPath();
}

QRectF OptimizedDrawingState::getCurrentBounds() const
{
    return m_pathBuilder.getCurrentBounds();
}

bool OptimizedDrawingState::hasDirtyRegions() const
{
    if (!m_dirtyRegionOptimizationEnabled) {
        return m_pathBuilder.hasPath();
    }
    
    return m_dirtyRegionManager.hasDirtyRegions();
}

void OptimizedDrawingState::clearDirtyRegions()
{
    m_dirtyRegionManager.clearDirtyRegions();
    m_pathBuilder.clearCache();
}

QRectF OptimizedDrawingState::getDirtyRegion()
{
    if (!m_dirtyRegionOptimizationEnabled) {
        return m_pathBuilder.getCurrentBounds();
    }

    // 获取当前需要重绘的区域
    QRectF redrawRegion = m_dirtyRegionManager.getMergedDirtyRegion();

    m_lastDrawnRegion = m_currentDrawRegion;
    // 清除脏区域管理器，为下次更新做准备
    m_dirtyRegionManager.clearDirtyRegions();

    return redrawRegion;
}

bool OptimizedDrawingState::hasPath() const
{
    return m_pathBuilder.hasPath();
}

bool OptimizedDrawingState::needsUpdate() const
{
    return m_pathBuilder.needsRebuild() || hasDirtyRegions();
}

void OptimizedDrawingState::setBatchSize(int size)
{
    m_pathBuilder.setBatchSize(size);
}

void OptimizedDrawingState::handleToolSpecificDrawing(const QPointF& point)
{
    if (!m_dirtyRegionOptimizationEnabled) {
        return;
    }

    qreal penWidth = qMax(m_pen.widthF(), 1.0);
    qreal margin = penWidth + 5.0; // 增加边距确保完全覆盖

    handleShapeToolDrawing(point, margin);

}

void OptimizedDrawingState::handleShapeToolDrawing(const QPointF& point, qreal margin)
{
    // 计算当前绘制区域
    QRectF currentRegion = calculateShapeRegion(point, margin);

    // 清除脏区域管理器
    m_dirtyRegionManager.clearDirtyRegions();

    // 使用连接区域方法，确保快速移动时不会有间隙
    m_dirtyRegionManager.addConnectedRegions(m_lastDrawnRegion, currentRegion);

    // 更新记录
    m_currentDrawRegion = currentRegion;
}

QRectF OptimizedDrawingState::calculateShapeRegion(const QPointF& point, qreal margin)
{
    QRectF region;

    switch (m_toolType) {
    case ToolType::Line:
    case ToolType::DashedLine:
        // 直线：创建包含整条线的矩形，需要normalized确保正确的边界
        region = QRectF(m_startPoint, point).normalized();
        region = region.adjusted(-margin, -margin, margin, margin);
        break;

    case ToolType::Rectangle:
    case ToolType::Square:
    case ToolType::Ellipse:
    case ToolType::Circle:
    case ToolType::Triangle:
    case ToolType::RightTriangle:
        {
            // 形状工具：使用工具的getBoundingRect方法，它会正确处理约束
            ShapeToolManager* manager = ShapeToolManager::instance();
            if (manager && manager->hasToolType(m_toolType)) {
                AbstractShapeTool* tool = manager->getTool(m_toolType);
                if (tool) {
                    region = tool->getBoundingRect(m_startPoint, point);
                    // 确保区域是正向的，用于脏区域计算
                    region = region.normalized();
                    region = region.adjusted(-margin, -margin, margin, margin);
                    break;
                }
            }
            // 回退处理
            region = QRectF(m_startPoint, point).normalized();
            region = region.adjusted(-margin, -margin, margin, margin);
        }
        break;

    case ToolType::Arrow:
        {
            // 箭头：需要更大的边距
            region = QRectF(m_startPoint, point).normalized();
            qreal arrowMargin = margin + 30.0;
            region = region.adjusted(-arrowMargin, -arrowMargin, arrowMargin, arrowMargin);
            break;
        }
    case ToolType::FreeDraw:
        region = QRectF(m_lastPoint, point).normalized();
        region = region.adjusted(-margin, -margin, margin, margin);
        break;

    default:
        // 默认处理
        region = QRectF(m_startPoint, point).normalized();
        region = region.adjusted(-margin, -margin, margin, margin);
        break;
    }

    return ensureMinimumRegionSize(region);
}

QRectF OptimizedDrawingState::ensureMinimumRegionSize(const QRectF& region) const
{
    if (region.isEmpty()) {
        return region;
    }

    QRectF result = region;

    // 确保宽度至少为最小尺寸
    if (result.width() < MIN_DIRTY_REGION_SIZE) {
        qreal expansion = (MIN_DIRTY_REGION_SIZE - result.width()) / 2.0;
        result.setLeft(result.left() - expansion);
        result.setRight(result.right() + expansion);
    }

    // 确保高度至少为最小尺寸
    if (result.height() < MIN_DIRTY_REGION_SIZE) {
        qreal expansion = (MIN_DIRTY_REGION_SIZE - result.height()) / 2.0;
        result.setTop(result.top() - expansion);
        result.setBottom(result.bottom() + expansion);
    }

    return result;
}

// 速度感应功能实现
void OptimizedDrawingState::setVelocitySettings(qreal minWidthRatio, qreal maxWidthRatio, qreal velocityThreshold)
{
    m_minWidthRatio = qBound(0.1, minWidthRatio, 1.0);
    m_maxWidthRatio = qBound(1.0, maxWidthRatio, 3.0);
    m_velocityThreshold = qMax(50.0, velocityThreshold);
}

QPainterPath OptimizedDrawingState::getVelocityAdjustedPath() const
{
    if (!m_velocityEnabled || m_velocityHistory.size() < 2) {
        return getCurrentPath();
    }

    return buildVariableWidthPath();
}

qreal OptimizedDrawingState::getCurrentVelocity() const
{
    if (m_velocityHistory.isEmpty()) {
        return 0.0;
    }
    return m_velocityHistory.last().velocity;
}

qreal OptimizedDrawingState::getCurrentCalculatedWidth() const
{
    if (m_velocityHistory.isEmpty()) {
        return m_baseWidth;
    }
    return m_velocityHistory.last().calculatedWidth;
}

void OptimizedDrawingState::updateVelocityData(const QPointF& point, qint64 timestamp)
{
    qreal velocity = calculateVelocity(point, timestamp);

    // 应用平滑
    velocity = applyVelocitySmoothing(velocity);

    // 计算基于速度的宽度
    qreal calculatedWidth = calculateWidthFromVelocity(velocity);

    // 创建速度数据
    VelocityData data(point, velocity, timestamp, calculatedWidth);
    m_velocityHistory.append(data);

    // 限制历史记录大小
    if (m_velocityHistory.size() > MAX_VELOCITY_HISTORY) {
        m_velocityHistory.removeFirst();
    }
}

qreal OptimizedDrawingState::calculateVelocity(const QPointF& currentPoint, qint64 currentTime)
{
    if (m_velocityHistory.isEmpty()) {
        return 0.0;
    }

    const VelocityData& lastData = m_velocityHistory.last();
    qreal timeDiff = currentTime - lastData.timestamp;

    // 避免除零和时间差太小的情况
    if (timeDiff < MIN_TIME_DIFF) {
        return lastData.velocity;
    }

    qreal distance = QLineF(lastData.position, currentPoint).length();
    qreal velocity = (distance / timeDiff) * 1000.0; // 转换为像素/秒

    return velocity;
}

qreal OptimizedDrawingState::calculateWidthFromVelocity(qreal velocity)
{
    if (velocity <= 0) {
        return m_baseWidth * m_maxWidthRatio;
    }

    // 使用指数衰减函数，提供更自然的宽度变化
    qreal normalizedVelocity = velocity / m_velocityThreshold;

    // 限制最大归一化速度
    normalizedVelocity = qMin(normalizedVelocity, 2.0);

    // 使用平滑的指数衰减
    qreal widthRatio = m_minWidthRatio + (m_maxWidthRatio - m_minWidthRatio) * qExp(-normalizedVelocity);

    return m_baseWidth * widthRatio;
}

qreal OptimizedDrawingState::applyVelocitySmoothing(qreal currentVelocity)
{
    if (m_velocityHistory.size() < 2) {
        return currentVelocity;
    }

    // 使用指数移动平均进行平滑
    qreal smoothedVelocity = currentVelocity;

    // 取最近几个速度值进行平滑
    int smoothingWindow = qMin(5, m_velocityHistory.size());
    qreal weightSum = 0.0;
    qreal velocitySum = 0.0;

    for (int i = 0; i < smoothingWindow; ++i) {
        int index = m_velocityHistory.size() - 1 - i;
        qreal weight = qPow(m_smoothingFactor, i);
        velocitySum += m_velocityHistory[index].velocity * weight;
        weightSum += weight;
    }

    if (weightSum > 0) {
        smoothedVelocity = (velocitySum + currentVelocity * (1.0 - m_smoothingFactor)) / (weightSum + (1.0 - m_smoothingFactor));
    }

    return smoothedVelocity;
}

QPainterPath OptimizedDrawingState::buildVariableWidthPath() const
{
    if (m_velocityHistory.size() < 2) {
        return QPainterPath();
    }

    QPainterPath path;

    // 为每个线段创建变宽度的四边形
    for (int i = 0; i < m_velocityHistory.size() - 1; ++i) {
        const VelocityData& data1 = m_velocityHistory[i];
        const VelocityData& data2 = m_velocityHistory[i + 1];

        addVariableWidthSegment(path, data1.position, data2.position,
                              data1.calculatedWidth, data2.calculatedWidth);
    }

    return path;
}

void OptimizedDrawingState::addVariableWidthSegment(QPainterPath& path, const QPointF& p1, const QPointF& p2,
                                                  qreal width1, qreal width2) const
{
    // 计算线段方向向量
    QPointF direction = p2 - p1;
    qreal length = qSqrt(direction.x() * direction.x() + direction.y() * direction.y());

    if (length < 0.001) {
        return; // 避免除零
    }

    // 归一化方向向量
    direction /= length;

    // 计算垂直向量（法向量）
    QPointF normal(-direction.y(), direction.x());

    // 计算四个顶点
    QPointF p1_left = p1 + normal * (width1 * 0.5);
    QPointF p1_right = p1 - normal * (width1 * 0.5);
    QPointF p2_left = p2 + normal * (width2 * 0.5);
    QPointF p2_right = p2 - normal * (width2 * 0.5);

    // 创建四边形路径
    if (path.isEmpty()) {
        path.moveTo(p1_left);
        path.lineTo(p2_left);
        path.lineTo(p2_right);
        path.lineTo(p1_right);
        path.closeSubpath();
    } else {
        // 连接到前一个四边形
        QPainterPath segment;
        segment.moveTo(p1_left);
        segment.lineTo(p2_left);
        segment.lineTo(p2_right);
        segment.lineTo(p1_right);
        segment.closeSubpath();

        path = path.united(segment);
    }
}