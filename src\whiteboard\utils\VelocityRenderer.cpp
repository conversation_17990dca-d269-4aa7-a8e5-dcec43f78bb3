#include "VelocityRenderer.h"
#include <QtMath>
#include <QRadialGradient>
#include <QMap>

// 静态成员初始化
QMap<QString, QPixmap> VelocityRenderer::s_textureCache;

void VelocityRenderer::renderVelocityPath(QPainter* painter, const VelocityDrawingState& velocityState, 
                                        RenderMode mode)
{
    if (!painter || !velocityState.hasVelocityData()) {
        return;
    }

    setupRenderingHints(painter, true);

    QVector<VelocityData> velocityData = velocityState.getVelocityHistory();
    QPen pen = velocityState.getPen();
    QBrush brush = velocityState.getBrush();

    switch (mode) {
    case RenderMode::VariableWidth:
        renderVariableWidthPath(painter, velocityData, pen, brush);
        break;
    case RenderMode::MultiStroke:
        renderMultiStrokePath(painter, velocityData, pen);
        break;
    case RenderMode::TextureBrush:
        renderTextureBrushPath(painter, velocityData, pen);
        break;
    }
}

void VelocityRenderer::renderVariableWidthPath(QPainter* painter, const QVector<VelocityData>& velocityData,
                                             const QPen& pen, const QBrush& brush)
{
    if (velocityData.size() < 2) {
        return;
    }

    painter->save();
    painter->setPen(Qt::NoPen);
    painter->setBrush(QBrush(pen.color()));

    QPainterPath combinedPath;

    // 为每个线段创建变宽度路径
    for (int i = 0; i < velocityData.size() - 1; ++i) {
        const VelocityData& data1 = velocityData[i];
        const VelocityData& data2 = velocityData[i + 1];

        QPainterPath segment = createVariableWidthSegment(
            data1.position, data2.position,
            data1.calculatedWidth, data2.calculatedWidth
        );

        if (i == 0) {
            combinedPath = segment;
        } else {
            combinedPath = combinedPath.united(segment);
        }
    }

    painter->drawPath(combinedPath);
    painter->restore();
}

void VelocityRenderer::renderMultiStrokePath(QPainter* painter, const QVector<VelocityData>& velocityData,
                                           const QPen& pen)
{
    if (velocityData.size() < 2) {
        return;
    }

    painter->save();

    // 绘制多层，从粗到细，从透明到不透明
    const int layers = 3;
    
    for (int layer = layers - 1; layer >= 0; --layer) {
        QPen layerPen = pen;
        QColor color = pen.color();
        
        // 计算当前层的参数
        qreal layerRatio = static_cast<qreal>(layer) / (layers - 1);
        qreal widthMultiplier = 1.0 + layerRatio * 0.5; // 外层更宽
        qreal alpha = 0.3 + 0.7 * (1.0 - layerRatio);   // 内层更不透明
        
        color.setAlphaF(alpha);
        layerPen.setColor(color);
        
        // 绘制每个线段
        for (int i = 0; i < velocityData.size() - 1; ++i) {
            const VelocityData& data1 = velocityData[i];
            const VelocityData& data2 = velocityData[i + 1];
            
            // 调整宽度
            qreal width1 = data1.calculatedWidth * widthMultiplier;
            qreal width2 = data2.calculatedWidth * widthMultiplier;
            
            layerPen.setWidthF((width1 + width2) * 0.5);
            painter->setPen(layerPen);
            painter->drawLine(data1.position, data2.position);
        }
    }

    painter->restore();
}

void VelocityRenderer::renderTextureBrushPath(QPainter* painter, const QVector<VelocityData>& velocityData,
                                            const QPen& pen)
{
    if (velocityData.isEmpty()) {
        return;
    }

    painter->save();
    painter->setCompositionMode(QPainter::CompositionMode_Multiply);

    QColor brushColor = pen.color();
    
    for (const VelocityData& data : velocityData) {
        int textureSize = static_cast<int>(data.calculatedWidth * 2);
        
        // 创建或获取缓存的纹理
        QString cacheKey = QString("%1_%2_%3_%4")
                          .arg(textureSize)
                          .arg(brushColor.red())
                          .arg(brushColor.green())
                          .arg(brushColor.blue());
        
        QPixmap texture;
        if (s_textureCache.contains(cacheKey)) {
            texture = s_textureCache[cacheKey];
        } else {
            texture = createCircularBrushTexture(textureSize, brushColor);
            s_textureCache[cacheKey] = texture;
            
            // 限制缓存大小
            if (s_textureCache.size() > 50) {
                s_textureCache.clear();
            }
        }
        
        // 绘制纹理
        QPointF center = data.position;
        painter->drawPixmap(center.x() - texture.width() / 2,
                          center.y() - texture.height() / 2,
                          texture);
    }

    painter->restore();
}

void VelocityRenderer::setupRenderingHints(QPainter* painter, bool highQuality)
{
    if (!painter) {
        return;
    }

    if (highQuality) {
        painter->setRenderHint(QPainter::Antialiasing, true);
        painter->setRenderHint(QPainter::SmoothPixmapTransform, true);
        painter->setRenderHint(QPainter::HighQualityAntialiasing, true);
    } else {
        painter->setRenderHint(QPainter::Antialiasing, true);
    }
}

QPainterPath VelocityRenderer::createVariableWidthSegment(const QPointF& p1, const QPointF& p2,
                                                        qreal width1, qreal width2)
{
    QPainterPath path;
    
    // 计算方向向量
    QPointF direction = p2 - p1;
    qreal length = qSqrt(direction.x() * direction.x() + direction.y() * direction.y());
    
    if (length < 0.001) {
        return path; // 避免除零
    }
    
    // 归一化
    direction /= length;
    
    // 计算法向量
    QPointF normal(-direction.y(), direction.x());
    
    // 计算四个顶点
    QPointF p1_left = p1 + normal * (width1 * 0.5);
    QPointF p1_right = p1 - normal * (width1 * 0.5);
    QPointF p2_left = p2 + normal * (width2 * 0.5);
    QPointF p2_right = p2 - normal * (width2 * 0.5);
    
    // 构建路径
    path.moveTo(p1_left);
    path.lineTo(p2_left);
    path.lineTo(p2_right);
    path.lineTo(p1_right);
    path.closeSubpath();
    
    return path;
}

QPixmap VelocityRenderer::createCircularBrushTexture(int size, const QColor& color)
{
    QPixmap texture(size, size);
    texture.fill(Qt::transparent);
    
    QPainter texturePainter(&texture);
    texturePainter.setRenderHint(QPainter::Antialiasing);
    
    // 创建径向渐变
    QRadialGradient gradient(size / 2, size / 2, size / 2);
    
    QColor centerColor = color;
    QColor edgeColor = color;
    edgeColor.setAlpha(0);
    
    gradient.setColorAt(0.0, centerColor);
    gradient.setColorAt(0.7, centerColor);
    gradient.setColorAt(1.0, edgeColor);
    
    texturePainter.setBrush(QBrush(gradient));
    texturePainter.setPen(Qt::NoPen);
    texturePainter.drawEllipse(0, 0, size, size);
    
    return texture;
}

QVector<QPointF> VelocityRenderer::interpolatePoints(const QPointF& p1, const QPointF& p2, int steps)
{
    QVector<QPointF> points;
    
    if (steps <= 0) {
        return points;
    }
    
    for (int i = 0; i <= steps; ++i) {
        qreal t = static_cast<qreal>(i) / steps;
        QPointF interpolated = p1 + t * (p2 - p1);
        points.append(interpolated);
    }
    
    return points;
}
